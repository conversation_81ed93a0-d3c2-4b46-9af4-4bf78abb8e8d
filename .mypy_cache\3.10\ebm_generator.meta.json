{"data_mtime": 1751189724, "dep_lines": [28, 29, 27, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 43, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 10, 5, 10, 10, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["extensions.reporting.html_generator", "extensions.visualization.data_visualizer", "extensions.visualization", "json", "logging", "collections", "typing", "os", "datetime", "random", "re", "llm_manager", "prompts", "config", "file_utils", "evidence_processor", "ebm_professional_enhancements", "time", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "extensions", "extensions.reporting", "extensions.visualization.base_plot", "extensions.visualization.forest_plot", "extensions.visualization.funnel_plot", "extensions.visualization.rob_plot", "types", "typing_extensions"], "hash": "d2f7d6b9c5c3ea27c799a060d94a6c63fb5387db", "id": "ebm_generator", "ignore_all": true, "interface_hash": "74c4c2e1b91864506e73e40e76f903e19c4b68a4", "mtime": 1751189705, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\ebm_generator.py", "plugin_data": null, "size": 134964, "suppressed": [], "version_id": "1.15.0"}