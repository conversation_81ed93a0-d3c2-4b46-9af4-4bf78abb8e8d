#!/usr/bin/env python3
"""
调试可视化问题的脚本
"""

import logging
import sys

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_visualization_availability():
    """测试可视化模块可用性"""
    try:
        # 测试DataVisualizer导入
        from extensions.visualization.data_visualizer import DataVisualizer
        logger.info("✅ DataVisualizer 直接导入成功")
        
        # 测试通过__init__.py导入
        from extensions.visualization import DataVisualizer as DataVisualizerInit
        logger.info("✅ DataVisualizer 通过__init__.py导入成功")
        
        # 测试EBM生成器中的VISUALIZATION_AVAILABLE
        import ebm_generator
        logger.info(f"📊 ebm_generator.VISUALIZATION_AVAILABLE = {ebm_generator.VISUALIZATION_AVAILABLE}")
        
        if not ebm_generator.VISUALIZATION_AVAILABLE:
            logger.error("❌ VISUALIZATION_AVAILABLE 为 False，这是问题所在！")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 可视化模块测试失败: {e}")
        return False

def test_data_visualizer_creation():
    """测试DataVisualizer创建和基本功能"""
    try:
        from extensions.visualization.data_visualizer import DataVisualizer
        
        # 创建实例
        visualizer = DataVisualizer("test_output")
        logger.info("✅ DataVisualizer 实例创建成功")
        
        # 测试数据准备
        test_data = {
            'search_stats': {
                'identified': 60,
                'after_duplicates': 50,
                'screened': 45,
                'full_text_assessed': 30,
                'included_qualitative': 20,
                'included_quantitative': 15,
                'duplicates_removed': 10,
                'excluded_screening': 15,
                'excluded_full_text': 10
            },
            'quality_summary': {
                'quality_distribution': {
                    'High': 2,
                    'Moderate': 8,
                    'Low': 7,
                    'Very Low': 3
                },
                'bias_risk_summary': {
                    'random_sequence_generation': {'Low': 5, 'Unclear': 3, 'High': 2},
                    'allocation_concealment': {'Low': 4, 'Unclear': 4, 'High': 2}
                }
            },
            'studies': [
                {'id': 'test1', 'title': 'Test Study 1', 'year': 2024},
                {'id': 'test2', 'title': 'Test Study 2', 'year': 2023}
            ]
        }
        
        # 尝试生成可视化
        logger.info("🎨 开始生成测试可视化...")
        charts = visualizer.generate_all_visualizations(test_data)
        
        logger.info(f"📊 生成结果: {len(charts)} 个图表")
        for chart_name, chart_path in charts.items():
            logger.info(f"  - {chart_name}: {chart_path}")
        
        return len(charts) > 0
        
    except Exception as e:
        logger.error(f"❌ DataVisualizer 测试失败: {e}", exc_info=True)
        return False

def test_evidence_data_access():
    """测试证据数据访问"""
    try:
        from ebm_generator import EBMGenerator
        from llm_manager import LLMManager
        
        # 创建实例
        llm_manager = LLMManager()
        
        def dummy_callback(message, is_error=False):
            logger.info(f"回调: {message}")
        
        generator = EBMGenerator(llm_manager, dummy_callback)
        
        # 模拟一些研究数据
        test_studies = [
            {
                'id': 'study1',
                'title': 'Test Study 1',
                'year': 2024,
                'authors': ['Author1'],
                'clinical_data': {
                    'effect_size': 1.5,
                    'ci_lower': 1.2,
                    'ci_upper': 1.8,
                    'weight': 10.0,
                    'metric': 'OR',
                    'sample_size': 200
                }
            },
            {
                'id': 'study2',
                'title': 'Test Study 2',
                'year': 2023,
                'authors': ['Author2'],
                'clinical_data': {
                    'effect_size': 0.8,
                    'ci_lower': 0.6,
                    'ci_upper': 1.0,
                    'weight': 8.0,
                    'metric': 'OR',
                    'sample_size': 150
                }
            }
        ]
        
        # 设置研究缓存
        generator.all_articles_cache = test_studies
        
        # 测试可视化数据准备
        viz_data = generator._prepare_comprehensive_visualization_data(test_studies)
        
        logger.info("✅ 可视化数据准备成功")
        logger.info(f"  - 搜索统计: {viz_data.get('search_stats', {})}")
        logger.info(f"  - 研究数量: {len(viz_data.get('studies', []))}")
        logger.info(f"  - 证据数据: {viz_data.get('evidence_data', {})}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 证据数据访问测试失败: {e}", exc_info=True)
        return False

def main():
    """运行所有调试测试"""
    logger.info("🚀 开始可视化调试测试...")
    
    tests = [
        ("可视化模块可用性", test_visualization_availability),
        ("DataVisualizer创建", test_data_visualizer_creation),
        ("证据数据访问", test_evidence_data_access)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    logger.info("\n📊 调试测试结果:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        logger.info("🎉 所有调试测试通过！可视化应该能正常工作。")
        return True
    else:
        logger.warning("⚠️ 部分调试测试失败，需要进一步修复。")
        return False

if __name__ == "__main__":
    success = main()
    print("\n" + "="*60)
    if success:
        print("✅ 可视化调试成功！")
        print("📊 DataVisualizer 应该能正常生成图表")
        print("🔧 真实数据应该能正确传递")
    else:
        print("❌ 可视化调试失败，需要进一步检查")
        print("🔍 请检查上面的错误信息")
    print("="*60)
    sys.exit(0 if success else 1)
