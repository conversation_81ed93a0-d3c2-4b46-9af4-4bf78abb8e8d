{".class": "MypyFile", "_fullname": "matplotlib.backends.registry", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BackendFilter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends.registry.BackendFilter", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "matplotlib.backends.registry.BackendFilter", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "matplotlib.backends.registry", "mro": ["matplotlib.backends.registry.BackendFilter", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "INTERACTIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends.registry.BackendFilter.INTERACTIVE", "name": "INTERACTIVE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "NON_INTERACTIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends.registry.BackendFilter.NON_INTERACTIVE", "name": "NON_INTERACTIVE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends.registry.BackendFilter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends.registry.BackendFilter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BackendRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends.registry.BackendRegistry", "name": "BackendRegistry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends.registry", "mro": ["matplotlib.backends.registry.BackendRegistry", "builtins.object"], "names": {".class": "SymbolTable", "_BUILTIN_BACKEND_TO_GUI_FRAMEWORK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends.registry.BackendRegistry._BUILTIN_BACKEND_TO_GUI_FRAMEWORK", "name": "_BUILTIN_BACKEND_TO_GUI_FRAMEWORK", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_GUI_FRAMEWORK_TO_BACKEND": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends.registry.BackendRegistry._GUI_FRAMEWORK_TO_BACKEND", "name": "_GUI_FRAMEWORK_TO_BACKEND", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry.__init__", "name": "__init__", "type": null}}, "_backend_module_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry._backend_module_name", "name": "_backend_module_name", "type": null}}, "_backend_to_gui_framework": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends.registry.BackendRegistry._backend_to_gui_framework", "name": "_backend_to_gui_framework", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry._clear", "name": "_clear", "type": null}}, "_ensure_entry_points_loaded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry._ensure_entry_points_loaded", "name": "_ensure_entry_points_loaded", "type": null}}, "_get_gui_framework_by_loading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry._get_gui_framework_by_loading", "name": "_get_gui_framework_by_loading", "type": null}}, "_loaded_entry_points": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends.registry.BackendRegistry._loaded_entry_points", "name": "_loaded_entry_points", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_name_to_module": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends.registry.BackendRegistry._name_to_module", "name": "_name_to_module", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_read_entry_points": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry._read_entry_points", "name": "_read_entry_points", "type": null}}, "_validate_and_store_entry_points": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entries"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry._validate_and_store_entry_points", "name": "_validate_and_store_entry_points", "type": null}}, "backend_for_gui_framework": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "framework"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry.backend_for_gui_framework", "name": "backend_for_gui_framework", "type": null}}, "is_valid_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry.is_valid_backend", "name": "is_valid_backend", "type": null}}, "list_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry.list_all", "name": "list_all", "type": null}}, "list_builtin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "filter_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry.list_builtin", "name": "list_builtin", "type": null}}, "list_gui_frameworks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry.list_gui_frameworks", "name": "list_gui_frameworks", "type": null}}, "load_backend_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry.load_backend_module", "name": "load_backend_module", "type": null}}, "resolve_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry.resolve_backend", "name": "resolve_backend", "type": null}}, "resolve_gui_or_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "gui_or_backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.registry.BackendRegistry.resolve_gui_or_backend", "name": "resolve_gui_or_backend", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends.registry.BackendRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends.registry.BackendRegistry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.registry.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.registry.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.registry.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.registry.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.registry.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.registry.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "backend_registry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends.registry.backend_registry", "name": "backend_registry", "type": "matplotlib.backends.registry.BackendRegistry"}}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}}, "path": "e:\\xzyxgg\\venv\\lib\\site-packages\\matplotlib\\backends\\registry.py"}