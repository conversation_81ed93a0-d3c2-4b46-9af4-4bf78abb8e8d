#!/usr/bin/env python3
"""
测试数据提取功能的脚本
"""

import sys
import json
import logging
from typing import Dict, Any, List

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 模拟的研究数据，包含真实的效应量信息
SAMPLE_STUDIES = [
    {
        "id": "12345678",
        "source": "PubMed",
        "title": "Effect of metformin on cardiovascular outcomes in diabetes",
        "abstract": "Background: Metformin is widely used for diabetes treatment. Methods: We conducted a randomized controlled trial with 1000 patients. Results: Metformin reduced cardiovascular events with OR 0.75 (95% CI: 0.60-0.94, p=0.012). The relative risk was 0.78 (95% CI: 0.65-0.93). Conclusion: Metformin shows cardiovascular benefits.",
        "authors": ["Smith J", "Johnson A", "Brown K"],
        "journal": "Diabetes Care",
        "year": "2023",
        "url": "https://pubmed.ncbi.nlm.nih.gov/12345678/",
        "doi": "10.2337/dc23-0001",
        "is_clinical": True,
        "ebm_data": {
            "is_clinical": True,
            "pico": {
                "population": "patients with diabetes",
                "intervention": "metformin",
                "comparison": "placebo",
                "outcome": "cardiovascular events"
            },
            "results": {
                "effect_size": "OR",
                "effect_value": 0.75,
                "ci_lower": 0.60,
                "ci_upper": 0.94,
                "p_value": "p=0.012"
            },
            "study_design": "RCT",
            "sample_size": 1000,
            "quality_indicators": {
                "randomized": True,
                "blinded": True,
                "controlled": True
            }
        }
    },
    {
        "id": "87654321",
        "source": "PubMed", 
        "title": "Statin therapy and stroke prevention: a meta-analysis",
        "abstract": "We analyzed 15 randomized trials including 45,000 patients. Statin therapy significantly reduced stroke risk (RR 0.82, 95% CI 0.74-0.91, p<0.001). The mean difference in LDL cholesterol was -1.2 mmol/L (95% CI: -1.5 to -0.9).",
        "authors": ["Wilson M", "Davis R"],
        "journal": "Stroke",
        "year": "2023",
        "url": "https://pubmed.ncbi.nlm.nih.gov/87654321/",
        "doi": "10.1161/str.2023.001",
        "is_clinical": True,
        "ebm_data": {
            "is_clinical": True,
            "pico": {
                "population": "patients at risk of stroke",
                "intervention": "statin therapy",
                "comparison": "placebo or no treatment",
                "outcome": "stroke incidence"
            },
            "results": {
                "effect_size": "RR",
                "effect_value": 0.82,
                "ci_lower": 0.74,
                "ci_upper": 0.91,
                "p_value": "p<0.001"
            },
            "study_design": "Meta-analysis",
            "sample_size": 45000,
            "quality_indicators": {
                "randomized": True,
                "blinded": False,
                "controlled": True
            }
        }
    },
    {
        "id": "11223344",
        "source": "PubMed",
        "title": "Exercise intervention for depression: randomized trial",
        "abstract": "A 12-week exercise program was compared to usual care in 200 patients with depression. The standardized mean difference in depression scores was -0.65 (95% CI: -0.95 to -0.35, p=0.001). Exercise group showed significant improvement.",
        "authors": ["Taylor S", "Anderson P"],
        "journal": "J Psychiatry",
        "year": "2023",
        "url": "https://pubmed.ncbi.nlm.nih.gov/11223344/",
        "doi": "10.1001/jpsych.2023.001",
        "is_clinical": True,
        "ebm_data": {
            "is_clinical": True,
            "pico": {
                "population": "patients with depression",
                "intervention": "exercise program",
                "comparison": "usual care",
                "outcome": "depression scores"
            },
            "results": {
                "effect_size": "SMD",
                "effect_value": -0.65,
                "ci_lower": -0.95,
                "ci_upper": -0.35,
                "p_value": "p=0.001"
            },
            "study_design": "RCT",
            "sample_size": 200,
            "quality_indicators": {
                "randomized": True,
                "blinded": False,
                "controlled": True
            }
        }
    }
]

def test_data_extraction():
    """测试数据提取功能"""
    try:
        # 导入必要的模块
        from evidence_processor import EvidenceProcessor
        
        # 创建一个模拟的LLM管理器
        class MockLLMManager:
            def generate_response(self, prompt, provider, model):
                # 返回模拟的PICO数据
                return json.dumps({
                    "study_design": "RCT",
                    "population": "patients with chronic disease",
                    "intervention": "drug therapy",
                    "comparison": "placebo",
                    "outcomes": "clinical outcomes",
                    "sample_size": "1000",
                    "key_findings": "significant improvement observed",
                    "quality_indicators": "randomized, double-blind"
                })
        
        # 创建证据处理器
        processor = EvidenceProcessor(MockLLMManager())
        
        logger.info("开始测试数据提取功能...")
        
        # 处理样本研究
        results = processor.process_study_batch(SAMPLE_STUDIES, "openai", "gpt-4")
        
        logger.info(f"处理结果: {results}")
        
        # 检查提取的可视化数据
        logger.info("\n=== 可视化数据提取结果 ===")
        for theme_name, theme_obj in processor.themes.items():
            logger.info(f"\n主题: {theme_name}")
            for i, evidence_point in enumerate(theme_obj.evidence_points):
                logger.info(f"  证据点 {i+1}:")
                logger.info(f"    研究ID: {evidence_point.study_id}")
                logger.info(f"    方向: {evidence_point.direction}")
                logger.info(f"    强度: {evidence_point.magnitude}")
                logger.info(f"    质量: {evidence_point.quality_score}")
                
                if evidence_point.visualization_data:
                    viz_data = evidence_point.visualization_data
                    logger.info(f"    可视化数据:")
                    logger.info(f"      研究标签: {viz_data.get('study_label')}")
                    logger.info(f"      效应量: {viz_data.get('effect_size')} ({viz_data.get('effect_type')})")
                    logger.info(f"      置信区间: [{viz_data.get('ci_lower')}, {viz_data.get('ci_upper')}]")
                    logger.info(f"      样本量: {viz_data.get('sample_size')}")
                    logger.info(f"      权重: {viz_data.get('weight')}")
                    logger.info(f"      P值: {viz_data.get('p_value')}")
                else:
                    logger.warning(f"    未提取到可视化数据")
        
        logger.info("\n测试完成!")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    success = test_data_extraction()
    sys.exit(0 if success else 1)
