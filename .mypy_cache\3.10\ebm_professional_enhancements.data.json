{".class": "MypyFile", "_fullname": "ebm_professional_enhancements", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ClinicalRecommendationGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ebm_professional_enhancements.ClinicalRecommendationGenerator", "name": "ClinicalRecommendationGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ClinicalRecommendationGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ebm_professional_enhancements", "mro": ["ebm_professional_enhancements.ClinicalRecommendationGenerator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "llm_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ClinicalRecommendationGenerator.__init__", "name": "__init__", "type": null}}, "_generate_conditional_recommendations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "evidence_summary", "quality_summary", "provider", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ClinicalRecommendationGenerator._generate_conditional_recommendations", "name": "_generate_conditional_recommendations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "evidence_summary", "quality_summary", "provider", "model"], "arg_types": ["ebm_professional_enhancements.ClinicalRecommendationGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_conditional_recommendations of ClinicalRecommendationGenerator", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_implementation_guidance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "evidence_summary", "provider", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ClinicalRecommendationGenerator._generate_implementation_guidance", "name": "_generate_implementation_guidance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "evidence_summary", "provider", "model"], "arg_types": ["ebm_professional_enhancements.ClinicalRecommendationGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_implementation_guidance of ClinicalRecommendationGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_monitoring_requirements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "evidence_summary", "provider", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ClinicalRecommendationGenerator._generate_monitoring_requirements", "name": "_generate_monitoring_requirements", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "evidence_summary", "provider", "model"], "arg_types": ["ebm_professional_enhancements.ClinicalRecommendationGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_monitoring_requirements of ClinicalRecommendationGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_primary_recommendations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "evidence_summary", "quality_summary", "provider", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ClinicalRecommendationGenerator._generate_primary_recommendations", "name": "_generate_primary_recommendations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "evidence_summary", "quality_summary", "provider", "model"], "arg_types": ["ebm_professional_enhancements.ClinicalRecommendationGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_primary_recommendations of ClinicalRecommendationGenerator", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_special_population_guidance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "evidence_summary", "provider", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ClinicalRecommendationGenerator._generate_special_population_guidance", "name": "_generate_special_population_guidance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "evidence_summary", "provider", "model"], "arg_types": ["ebm_professional_enhancements.ClinicalRecommendationGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_special_population_guidance of ClinicalRecommendationGenerator", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_clinical_recommendations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "evidence_summary", "quality_summary", "provider", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ClinicalRecommendationGenerator.generate_clinical_recommendations", "name": "generate_clinical_recommendations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "evidence_summary", "quality_summary", "provider", "model"], "arg_types": ["ebm_professional_enhancements.ClinicalRecommendationGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_clinical_recommendations of ClinicalRecommendationGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "llm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "ebm_professional_enhancements.ClinicalRecommendationGenerator.llm", "name": "llm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ebm_professional_enhancements.ClinicalRecommendationGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ebm_professional_enhancements.ClinicalRecommendationGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Counter": {".class": "SymbolTableNode", "cross_ref": "collections.Counter", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EconomicEvaluationProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ebm_professional_enhancements.EconomicEvaluationProcessor", "name": "EconomicEvaluationProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.EconomicEvaluationProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ebm_professional_enhancements", "mro": ["ebm_professional_enhancements.EconomicEvaluationProcessor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "llm_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.EconomicEvaluationProcessor.__init__", "name": "__init__", "type": null}}, "analyze_cost_effectiveness": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "studies", "provider", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.EconomicEvaluationProcessor.analyze_cost_effectiveness", "name": "analyze_cost_effectiveness", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "studies", "provider", "model"], "arg_types": ["ebm_professional_enhancements.EconomicEvaluationProcessor", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_cost_effectiveness of EconomicEvaluationProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "llm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "ebm_professional_enhancements.EconomicEvaluationProcessor.llm", "name": "llm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ebm_professional_enhancements.EconomicEvaluationProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ebm_professional_enhancements.EconomicEvaluationProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "EvidenceLevel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ebm_professional_enhancements.EvidenceLevel", "name": "EvidenceLevel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "ebm_professional_enhancements.EvidenceLevel", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "ebm_professional_enhancements", "mro": ["ebm_professional_enhancements.EvidenceLevel", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "LEVEL_1A": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ebm_professional_enhancements.EvidenceLevel.LEVEL_1A", "name": "LEVEL_1A", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "1a"}, "type_ref": "builtins.str"}}}, "LEVEL_1B": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ebm_professional_enhancements.EvidenceLevel.LEVEL_1B", "name": "LEVEL_1B", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "1b"}, "type_ref": "builtins.str"}}}, "LEVEL_2A": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ebm_professional_enhancements.EvidenceLevel.LEVEL_2A", "name": "LEVEL_2A", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "2a"}, "type_ref": "builtins.str"}}}, "LEVEL_2B": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ebm_professional_enhancements.EvidenceLevel.LEVEL_2B", "name": "LEVEL_2B", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "2b"}, "type_ref": "builtins.str"}}}, "LEVEL_3A": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ebm_professional_enhancements.EvidenceLevel.LEVEL_3A", "name": "LEVEL_3A", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "3a"}, "type_ref": "builtins.str"}}}, "LEVEL_3B": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ebm_professional_enhancements.EvidenceLevel.LEVEL_3B", "name": "LEVEL_3B", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "3b"}, "type_ref": "builtins.str"}}}, "LEVEL_4": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ebm_professional_enhancements.EvidenceLevel.LEVEL_4", "name": "LEVEL_4", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "4"}, "type_ref": "builtins.str"}}}, "LEVEL_5": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ebm_professional_enhancements.EvidenceLevel.LEVEL_5", "name": "LEVEL_5", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "5"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ebm_professional_enhancements.EvidenceLevel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ebm_professional_enhancements.EvidenceLevel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ProfessionalEBMProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ebm_professional_enhancements.ProfessionalEBMProcessor", "name": "ProfessionalEBMProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ProfessionalEBMProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ebm_professional_enhancements", "mro": ["ebm_professional_enhancements.ProfessionalEBMProcessor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "llm_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ProfessionalEBMProcessor.__init__", "name": "__init__", "type": null}}, "_assess_risk_of_bias_detailed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "study", "provider", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ProfessionalEBMProcessor._assess_risk_of_bias_detailed", "name": "_assess_risk_of_bias_detailed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "study", "provider", "model"], "arg_types": ["ebm_professional_enhancements.ProfessionalEBMProcessor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assess_risk_of_bias_detailed of ProfessionalEBMProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_default_bias_assessment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ProfessionalEBMProcessor._default_bias_assessment", "name": "_default_bias_assessment", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ebm_professional_enhancements.ProfessionalEBMProcessor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_default_bias_assessment of ProfessionalEBMProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_determine_evidence_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "study"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ProfessionalEBMProcessor._determine_evidence_level", "name": "_determine_evidence_level", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "study"], "arg_types": ["ebm_professional_enhancements.ProfessionalEBMProcessor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_determine_evidence_level of ProfessionalEBMProcessor", "ret_type": "ebm_professional_enhancements.EvidenceLevel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_overall_recommendation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ProfessionalEBMProcessor._generate_overall_recommendation", "name": "_generate_overall_recommendation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ebm_professional_enhancements.ProfessionalEBMProcessor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_overall_recommendation of ProfessionalEBMProcessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_grade_assessment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "study", "risk_of_bias", "provider", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ProfessionalEBMProcessor._grade_assessment", "name": "_grade_assessment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "study", "risk_of_bias", "provider", "model"], "arg_types": ["ebm_professional_enhancements.ProfessionalEBMProcessor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_grade_assessment of ProfessionalEBMProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assess_study_quality": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "study", "provider", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ProfessionalEBMProcessor.assess_study_quality", "name": "assess_study_quality", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "study", "provider", "model"], "arg_types": ["ebm_professional_enhancements.ProfessionalEBMProcessor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assess_study_quality of ProfessionalEBMProcessor", "ret_type": "ebm_professional_enhancements.QualityAssessment", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_quality_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.ProfessionalEBMProcessor.generate_quality_summary", "name": "generate_quality_summary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "arg_types": ["ebm_professional_enhancements.ProfessionalEBMProcessor", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_quality_summary of ProfessionalEBMProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "llm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "ebm_professional_enhancements.ProfessionalEBMProcessor.llm", "name": "llm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "quality_assessments": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "ebm_professional_enhancements.ProfessionalEBMProcessor.quality_assessments", "name": "quality_assessments", "type": {".class": "Instance", "args": ["builtins.str", "ebm_professional_enhancements.QualityAssessment"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ebm_professional_enhancements.ProfessionalEBMProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ebm_professional_enhancements.ProfessionalEBMProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QualityAssessment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ebm_professional_enhancements.QualityAssessment", "name": "QualityAssessment", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.QualityAssessment", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 41, "name": "study_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 42, "name": "evidence_level", "type": "ebm_professional_enhancements.EvidenceLevel"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 43, "name": "risk_of_bias", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 44, "name": "overall_quality", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 45, "name": "grade_certainty", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 46, "name": "limitations", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "inconsistency", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "indirectness", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 49, "name": "imprecision", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "publication_bias", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "ebm_professional_enhancements", "mro": ["ebm_professional_enhancements.QualityAssessment", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "ebm_professional_enhancements.QualityAssessment.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "study_id", "evidence_level", "risk_of_bias", "overall_quality", "grade_certainty", "limitations", "inconsistency", "indirectness", "imprecision", "publication_bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_professional_enhancements.QualityAssessment.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "study_id", "evidence_level", "risk_of_bias", "overall_quality", "grade_certainty", "limitations", "inconsistency", "indirectness", "imprecision", "publication_bias"], "arg_types": ["ebm_professional_enhancements.QualityAssessment", "builtins.str", "ebm_professional_enhancements.EvidenceLevel", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QualityAssessment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "ebm_professional_enhancements.QualityAssessment.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "study_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "evidence_level"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "risk_of_bias"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "overall_quality"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "grade_certainty"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "limitations"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "inconsistency"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "indirectness"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "imprecision"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "publication_bias"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["study_id", "evidence_level", "risk_of_bias", "overall_quality", "grade_certainty", "limitations", "inconsistency", "indirectness", "imprecision", "publication_bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "ebm_professional_enhancements.QualityAssessment.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["study_id", "evidence_level", "risk_of_bias", "overall_quality", "grade_certainty", "limitations", "inconsistency", "indirectness", "imprecision", "publication_bias"], "arg_types": ["builtins.str", "ebm_professional_enhancements.EvidenceLevel", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of QualityAssessment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "ebm_professional_enhancements.QualityAssessment.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["study_id", "evidence_level", "risk_of_bias", "overall_quality", "grade_certainty", "limitations", "inconsistency", "indirectness", "imprecision", "publication_bias"], "arg_types": ["builtins.str", "ebm_professional_enhancements.EvidenceLevel", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of QualityAssessment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "evidence_level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ebm_professional_enhancements.QualityAssessment.evidence_level", "name": "evidence_level", "type": "ebm_professional_enhancements.EvidenceLevel"}}, "grade_certainty": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ebm_professional_enhancements.QualityAssessment.grade_certainty", "name": "grade_certainty", "type": "builtins.str"}}, "imprecision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ebm_professional_enhancements.QualityAssessment.imprecision", "name": "imprecision", "type": "builtins.str"}}, "inconsistency": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ebm_professional_enhancements.QualityAssessment.inconsistency", "name": "inconsistency", "type": "builtins.str"}}, "indirectness": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ebm_professional_enhancements.QualityAssessment.indirectness", "name": "indirectness", "type": "builtins.str"}}, "limitations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ebm_professional_enhancements.QualityAssessment.limitations", "name": "limitations", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "overall_quality": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ebm_professional_enhancements.QualityAssessment.overall_quality", "name": "overall_quality", "type": "builtins.str"}}, "publication_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ebm_professional_enhancements.QualityAssessment.publication_bias", "name": "publication_bias", "type": "builtins.str"}}, "risk_of_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ebm_professional_enhancements.QualityAssessment.risk_of_bias", "name": "risk_of_bias", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "study_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ebm_professional_enhancements.QualityAssessment.study_id", "name": "study_id", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ebm_professional_enhancements.QualityAssessment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ebm_professional_enhancements.QualityAssessment", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RecommendationGrade": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ebm_professional_enhancements.RecommendationGrade", "name": "RecommendationGrade", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "ebm_professional_enhancements.RecommendationGrade", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "ebm_professional_enhancements", "mro": ["ebm_professional_enhancements.RecommendationGrade", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "GRADE_A": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ebm_professional_enhancements.RecommendationGrade.GRADE_A", "name": "GRADE_A", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "A"}, "type_ref": "builtins.str"}}}, "GRADE_B": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ebm_professional_enhancements.RecommendationGrade.GRADE_B", "name": "GRADE_B", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "B"}, "type_ref": "builtins.str"}}}, "GRADE_C": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ebm_professional_enhancements.RecommendationGrade.GRADE_C", "name": "GRADE_C", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "C"}, "type_ref": "builtins.str"}}}, "GRADE_D": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ebm_professional_enhancements.RecommendationGrade.GRADE_D", "name": "GRADE_D", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "D"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ebm_professional_enhancements.RecommendationGrade.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ebm_professional_enhancements.RecommendationGrade", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ebm_professional_enhancements.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ebm_professional_enhancements.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ebm_professional_enhancements.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ebm_professional_enhancements.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ebm_professional_enhancements.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ebm_professional_enhancements.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "ebm_professional_enhancements.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}}, "path": "E:\\xzyxgg\\ebm_professional_enhancements.py"}