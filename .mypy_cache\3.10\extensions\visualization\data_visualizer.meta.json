{"data_mtime": 1751194621, "dep_lines": [15, 16, 7, 8, 9, 10, 11, 15, 19, 31, 1, 1, 1, 1, 17, 18], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 20, 10, 10, 5, 30, 30, 30, 10, 10], "dependencies": ["matplotlib.pyplot", "matplotlib.patches", "os", "json", "logging", "typing", "pathlib", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "numpy", "warnings", "builtins", "_frozen_importlib", "abc", "types"], "hash": "d46e1e65f3a7264fe36e4a04bc0bba871df7721d", "id": "extensions.visualization.data_visualizer", "ignore_all": true, "interface_hash": "fce49da28cbe31be661265f433e2acf2bc5a2790", "mtime": 1751194513, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\extensions\\visualization\\data_visualizer.py", "plugin_data": null, "size": 16705, "suppressed": ["seaborn", "pandas"], "version_id": "1.15.0"}