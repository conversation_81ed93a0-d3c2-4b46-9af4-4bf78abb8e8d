{"data_mtime": 1751189388, "dep_lines": [10, 11, 7, 8, 9, 10, 14, 15, 16, 17, 1, 1, 1, 12, 13], "dep_prios": [10, 10, 10, 10, 10, 20, 10, 5, 5, 10, 5, 30, 30, 10, 10], "dependencies": ["matplotlib.pyplot", "matplotlib.patches", "os", "json", "logging", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "numpy", "typing", "pathlib", "warnings", "builtins", "_frozen_importlib", "abc"], "hash": "3fe6f3514f70f43523da121563f4379125a9145c", "id": "extensions.visualization.data_visualizer", "ignore_all": true, "interface_hash": "ab875f34002c7af7f9402772a33536634f8bc3c7", "mtime": 1751189334, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\extensions\\visualization\\data_visualizer.py", "plugin_data": null, "size": 14137, "suppressed": ["seaborn", "pandas"], "version_id": "1.15.0"}