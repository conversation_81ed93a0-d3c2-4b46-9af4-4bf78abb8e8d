#!/usr/bin/env python3
"""
数据可视化模块
为EBM报告生成各种图表和可视化内容
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# 尝试导入可视化库，如果失败则使用文本替代
try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as mpatches
    import seaborn as sns
    import pandas as pd
    import numpy as np

    # 设置中文字体和样式
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    sns.set_style("whitegrid")

    MATPLOTLIB_AVAILABLE = True
except ImportError as e:
    MATPLOTLIB_AVAILABLE = False
    logging.getLogger(__name__).warning(f"Matplotlib不可用，将使用文本替代: {e}")

import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class DataVisualizer:
    """数据可视化器"""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建图片子目录
        self.images_dir = self.output_dir / "images"
        self.images_dir.mkdir(exist_ok=True)
        
    def generate_prisma_flow_chart(self, search_stats: Dict[str, Any]) -> str:
        """生成PRISMA流程图"""
        try:
            if not MATPLOTLIB_AVAILABLE:
                return self._generate_text_prisma_chart(search_stats)

            fig, ax = plt.subplots(figsize=(12, 10))
            ax.set_xlim(0, 10)
            ax.set_ylim(0, 12)
            ax.axis('off')
            
            # 定义框的位置和大小
            boxes = [
                {"text": f"数据库检索识别的记录\n(n = {search_stats.get('identified', 0)})", 
                 "xy": (5, 11), "width": 3, "height": 0.8, "color": "lightblue"},
                {"text": f"去除重复后的记录\n(n = {search_stats.get('after_duplicates', 0)})", 
                 "xy": (5, 9.5), "width": 3, "height": 0.8, "color": "lightgreen"},
                {"text": f"筛选的记录\n(n = {search_stats.get('screened', 0)})", 
                 "xy": (5, 8), "width": 3, "height": 0.8, "color": "lightyellow"},
                {"text": f"全文评估的文章\n(n = {search_stats.get('full_text_assessed', 0)})", 
                 "xy": (5, 6.5), "width": 3, "height": 0.8, "color": "lightcoral"},
                {"text": f"纳入定性综合的研究\n(n = {search_stats.get('included_qualitative', 0)})", 
                 "xy": (5, 5), "width": 3, "height": 0.8, "color": "lightpink"},
                {"text": f"纳入定量综合的研究\n(荟萃分析)\n(n = {search_stats.get('included_quantitative', 0)})", 
                 "xy": (5, 3.5), "width": 3, "height": 1, "color": "lavender"}
            ]
            
            # 绘制框和文本
            for box in boxes:
                rect = mpatches.FancyBboxPatch(
                    (box["xy"][0] - box["width"]/2, box["xy"][1] - box["height"]/2),
                    box["width"], box["height"],
                    boxstyle="round,pad=0.1",
                    facecolor=box["color"],
                    edgecolor="black",
                    linewidth=1
                )
                ax.add_patch(rect)
                ax.text(box["xy"][0], box["xy"][1], box["text"], 
                       ha='center', va='center', fontsize=10, weight='bold')
            
            # 绘制箭头
            arrows = [
                ((5, 10.6), (5, 10.3)),  # 识别 -> 去重
                ((5, 9.1), (5, 8.8)),    # 去重 -> 筛选
                ((5, 7.6), (5, 7.3)),    # 筛选 -> 全文评估
                ((5, 6.1), (5, 5.8)),    # 全文评估 -> 定性综合
                ((5, 4.5), (5, 4.5))     # 定性 -> 定量综合
            ]
            
            for start, end in arrows:
                ax.annotate('', xy=end, xytext=start,
                           arrowprops=dict(arrowstyle='->', lw=2, color='black'))
            
            # 添加排除框
            exclusion_boxes = [
                {"text": f"排除重复记录\n(n = {search_stats.get('duplicates_removed', 0)})", 
                 "xy": (8.5, 9.5), "width": 2.5, "height": 0.6},
                {"text": f"排除不相关记录\n(n = {search_stats.get('excluded_screening', 0)})", 
                 "xy": (8.5, 8), "width": 2.5, "height": 0.6},
                {"text": f"排除的全文文章\n(n = {search_stats.get('excluded_full_text', 0)})", 
                 "xy": (8.5, 6.5), "width": 2.5, "height": 0.6}
            ]
            
            for box in exclusion_boxes:
                rect = mpatches.FancyBboxPatch(
                    (box["xy"][0] - box["width"]/2, box["xy"][1] - box["height"]/2),
                    box["width"], box["height"],
                    boxstyle="round,pad=0.05",
                    facecolor="mistyrose",
                    edgecolor="red",
                    linewidth=1,
                    linestyle='--'
                )
                ax.add_patch(rect)
                ax.text(box["xy"][0], box["xy"][1], box["text"], 
                       ha='center', va='center', fontsize=9)
            
            plt.title("PRISMA 2020 流程图", fontsize=16, weight='bold', pad=20)
            
            # 保存图片
            filename = "prisma_flow_chart.png"
            filepath = self.images_dir / filename
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            logger.info(f"PRISMA流程图已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"生成PRISMA流程图失败: {e}")
            return ""
    
    def generate_risk_of_bias_summary(self, bias_data: Dict[str, Any]) -> str:
        """生成偏倚风险总结图"""
        try:
            if not bias_data or 'bias_risk_summary' not in bias_data:
                logger.warning("偏倚风险数据为空，跳过图表生成")
                return ""

            bias_summary = bias_data['bias_risk_summary']
            if not bias_summary:
                logger.warning("偏倚风险总结数据为空")
                return ""

            if not MATPLOTLIB_AVAILABLE:
                return self._generate_text_bias_summary(bias_summary)
            
            # 准备数据
            domains = list(bias_summary.keys())
            risk_levels = ['Low', 'Unclear', 'High']
            colors = {'Low': 'green', 'Unclear': 'yellow', 'High': 'red'}
            
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 创建堆叠条形图数据
            data_matrix = []
            for domain in domains:
                domain_data = bias_summary[domain]
                row = [domain_data.get(level, 0) for level in risk_levels]
                data_matrix.append(row)
            
            data_matrix = np.array(data_matrix)
            
            # 绘制堆叠条形图
            bottom = np.zeros(len(domains))
            for i, level in enumerate(risk_levels):
                values = data_matrix[:, i]
                ax.barh(domains, values, left=bottom, 
                       color=colors[level], label=f'{level} risk', alpha=0.8)
                bottom += values
            
            ax.set_xlabel('研究数量', fontsize=12)
            ax.set_title('偏倚风险评估总结', fontsize=14, weight='bold')
            ax.legend(loc='upper right')
            
            # 美化图表
            ax.grid(axis='x', alpha=0.3)
            plt.tight_layout()
            
            # 保存图片
            filename = "risk_of_bias_summary.png"
            filepath = self.images_dir / filename
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            logger.info(f"偏倚风险总结图已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"生成偏倚风险总结图失败: {e}")
            return ""
    
    def generate_evidence_quality_chart(self, quality_data: Dict[str, Any]) -> str:
        """生成证据质量分布图"""
        try:
            if not quality_data or 'quality_distribution' not in quality_data:
                logger.warning("证据质量数据为空，跳过图表生成")
                return ""
            
            quality_dist = quality_data['quality_distribution']
            if not quality_dist:
                logger.warning("证据质量分布数据为空")
                return ""
            
            # 准备数据
            qualities = list(quality_dist.keys())
            counts = list(quality_dist.values())
            colors = ['#2E8B57', '#FFD700', '#FF6347', '#8B0000']  # 绿到红的渐变
            
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # 饼图
            ax1.pie(counts, labels=qualities, colors=colors[:len(qualities)], 
                   autopct='%1.1f%%', startangle=90)
            ax1.set_title('证据质量分布', fontsize=14, weight='bold')
            
            # 条形图
            bars = ax2.bar(qualities, counts, color=colors[:len(qualities)], alpha=0.8)
            ax2.set_xlabel('证据质量等级', fontsize=12)
            ax2.set_ylabel('研究数量', fontsize=12)
            ax2.set_title('证据质量分布', fontsize=14, weight='bold')
            
            # 在条形图上添加数值标签
            for bar, count in zip(bars, counts):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{count}', ha='center', va='bottom', fontsize=10)
            
            plt.tight_layout()
            
            # 保存图片
            filename = "evidence_quality_chart.png"
            filepath = self.images_dir / filename
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            logger.info(f"证据质量分布图已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"生成证据质量分布图失败: {e}")
            return ""
    
    def generate_study_characteristics_table(self, studies: List[Dict[str, Any]]) -> str:
        """生成研究特征表格的可视化"""
        try:
            if not studies:
                logger.warning("研究数据为空，跳过表格生成")
                return ""
            
            # 准备表格数据
            table_data = []
            for study in studies[:10]:  # 限制显示前10个研究
                row = {
                    '研究ID': study.get('id', 'N/A')[:10],
                    '发表年份': study.get('year', 'N/A'),
                    '研究类型': study.get('study_type', 'N/A'),
                    '样本量': study.get('sample_size', 'N/A'),
                    '干预措施': str(study.get('intervention', 'N/A'))[:20] + '...' if len(str(study.get('intervention', 'N/A'))) > 20 else str(study.get('intervention', 'N/A'))
                }
                table_data.append(row)
            
            df = pd.DataFrame(table_data)
            
            # 创建表格可视化
            fig, ax = plt.subplots(figsize=(14, 8))
            ax.axis('tight')
            ax.axis('off')
            
            # 创建表格
            table = ax.table(cellText=df.values, colLabels=df.columns,
                           cellLoc='center', loc='center',
                           colWidths=[0.15, 0.1, 0.15, 0.1, 0.3])
            
            # 美化表格
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1.2, 1.5)
            
            # 设置表头样式
            for i in range(len(df.columns)):
                table[(0, i)].set_facecolor('#4CAF50')
                table[(0, i)].set_text_props(weight='bold', color='white')
            
            # 设置交替行颜色
            for i in range(1, len(df) + 1):
                for j in range(len(df.columns)):
                    if i % 2 == 0:
                        table[(i, j)].set_facecolor('#f0f0f0')
            
            plt.title('纳入研究特征总结表', fontsize=16, weight='bold', pad=20)
            
            # 保存图片
            filename = "study_characteristics_table.png"
            filepath = self.images_dir / filename
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            logger.info(f"研究特征表格已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"生成研究特征表格失败: {e}")
            return ""
    
    def generate_all_visualizations(self, data: Dict[str, Any]) -> Dict[str, str]:
        """生成所有可视化图表"""
        visualizations = {}

        try:
            logger.info(f"开始生成可视化图表，matplotlib可用: {MATPLOTLIB_AVAILABLE}")

            # 生成PRISMA流程图
            if 'search_stats' in data:
                logger.info("生成PRISMA流程图...")
                prisma_path = self.generate_prisma_flow_chart(data['search_stats'])
                if prisma_path:
                    visualizations['prisma_flow_chart'] = prisma_path
                    logger.info(f"PRISMA流程图已生成: {prisma_path}")

            # 生成偏倚风险总结图
            if 'quality_summary' in data:
                logger.info("生成偏倚风险总结图...")
                bias_path = self.generate_risk_of_bias_summary(data['quality_summary'])
                if bias_path:
                    visualizations['risk_of_bias_summary'] = bias_path
                    logger.info(f"偏倚风险总结图已生成: {bias_path}")

            # 生成证据质量分布图
            if 'quality_summary' in data:
                logger.info("生成证据质量分布图...")
                quality_path = self.generate_evidence_quality_chart(data['quality_summary'])
                if quality_path:
                    visualizations['evidence_quality_chart'] = quality_path
                    logger.info(f"证据质量分布图已生成: {quality_path}")

            # 生成研究特征表格
            if 'studies' in data:
                logger.info("生成研究特征表格...")
                table_path = self.generate_study_characteristics_table(data['studies'])
                if table_path:
                    visualizations['study_characteristics_table'] = table_path
                    logger.info(f"研究特征表格已生成: {table_path}")

            logger.info(f"成功生成了 {len(visualizations)} 个可视化图表")
            return visualizations

        except Exception as e:
            logger.error(f"生成可视化图表时出错: {e}", exc_info=True)
            return visualizations

    def _generate_text_prisma_chart(self, search_stats: Dict[str, Any]) -> str:
        """生成文本版PRISMA流程图（当matplotlib不可用时）"""
        try:
            # 创建文本版本的PRISMA图表
            filename = "prisma_flow_chart_text.md"
            filepath = self.images_dir / filename

            content = f"""# PRISMA 2020 流程图

## 文献筛选流程

1. **数据库检索识别的记录**: {search_stats.get('identified', 0)} 篇
2. **去除重复后的记录**: {search_stats.get('after_duplicates', 0)} 篇
3. **筛选的记录**: {search_stats.get('screened', 0)} 篇
4. **全文评估的文章**: {search_stats.get('full_text_assessed', 0)} 篇
5. **纳入定性综合的研究**: {search_stats.get('included_qualitative', 0)} 篇
6. **纳入定量综合的研究（荟萃分析）**: {search_stats.get('included_quantitative', 0)} 篇

## 排除情况

- **排除重复记录**: {search_stats.get('duplicates_removed', 0)} 篇
- **排除不相关记录**: {search_stats.get('excluded_screening', 0)} 篇
- **排除的全文文章**: {search_stats.get('excluded_full_text', 0)} 篇
"""

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            logger.info(f"文本版PRISMA流程图已保存: {filepath}")
            return str(filepath)

        except Exception as e:
            logger.error(f"生成文本版PRISMA流程图失败: {e}")
            return ""
