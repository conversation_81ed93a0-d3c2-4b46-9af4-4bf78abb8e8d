#!/usr/bin/env python3
"""
测试文献搜索修复的脚本
"""

import logging
from config import settings
from llm_manager import LLMManager
from literature_search import SearchService

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_search_fix():
    """测试搜索修复是否有效"""
    
    # 初始化服务
    llm_manager = LLMManager()
    search_service = SearchService(llm_manager)
    
    # 设置LLM配置（如果需要）
    search_service.set_llm_config("ZhipuAI", "glm-4-flash-250414")
    
    # 执行搜索
    query = "病毒性肝炎"
    logger.info(f"开始搜索: {query}")
    
    results = search_service.search_all(query)
    
    logger.info(f"搜索完成，共找到 {len(results)} 篇文献")
    
    # 按提供者统计结果
    provider_stats = {}
    for article in results:
        source = article.get('source', 'Unknown')
        if source not in provider_stats:
            provider_stats[source] = 0
        provider_stats[source] += 1
    
    logger.info("按提供者统计:")
    for provider, count in provider_stats.items():
        logger.info(f"  {provider}: {count} 篇")
    
    # 显示前几篇文章的标题
    logger.info("前10篇文章标题:")
    for i, article in enumerate(results[:10], 1):
        title = article.get('title', 'No title')[:100]
        year = article.get('year', 'Unknown')
        source = article.get('source', 'Unknown')
        logger.info(f"  {i}. [{year}] {title}... (来源: {source})")
    
    return results

if __name__ == "__main__":
    try:
        results = test_search_fix()
        print(f"\n✅ 测试完成！共找到 {len(results)} 篇文献")
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)
        print(f"\n❌ 测试失败: {e}")
