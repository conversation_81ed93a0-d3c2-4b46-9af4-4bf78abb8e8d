#!/usr/bin/env python3
"""
测试可视化修复的脚本
"""

import logging
import os
import sys

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_visualization_import():
    """测试可视化模块导入"""
    try:
        # 测试新的导入方式
        from extensions.visualization import DataVisualizer
        logger.info("✅ DataVisualizer 导入成功")
        
        # 测试其他可视化组件
        from extensions.visualization import ForestPlot, FunnelPlot, RobPlot
        logger.info("✅ 其他可视化组件导入成功")
        
        return True
    except Exception as e:
        logger.error(f"❌ 可视化模块导入失败: {e}")
        return False

def test_ebm_generator_import():
    """测试EBM生成器导入"""
    try:
        from ebm_generator import EBMGenerator
        logger.info("✅ EBMGenerator 导入成功")
        
        # 检查VISUALIZATION_AVAILABLE状态
        import ebm_generator
        if hasattr(ebm_generator, 'VISUALIZATION_AVAILABLE'):
            status = ebm_generator.VISUALIZATION_AVAILABLE
            logger.info(f"📊 VISUALIZATION_AVAILABLE = {status}")
            return status
        else:
            logger.warning("⚠️ VISUALIZATION_AVAILABLE 变量不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ EBMGenerator 导入失败: {e}")
        return False

def test_data_visualizer_creation():
    """测试DataVisualizer创建"""
    try:
        from extensions.visualization import DataVisualizer
        
        # 创建测试输出目录
        test_dir = "test_viz_output"
        os.makedirs(test_dir, exist_ok=True)
        
        # 创建DataVisualizer实例
        visualizer = DataVisualizer(test_dir)
        logger.info("✅ DataVisualizer 实例创建成功")
        
        # 测试数据准备
        test_data = {
            'search_stats': {
                'identified': 50,
                'after_duplicates': 40,
                'screened': 35,
                'full_text_assessed': 25,
                'included_qualitative': 20,
                'included_quantitative': 15,
                'duplicates_removed': 10,
                'excluded_screening': 10,
                'excluded_full_text': 5
            },
            'quality_summary': {
                'quality_distribution': {
                    'High': 3,
                    'Moderate': 7,
                    'Low': 8,
                    'Very Low': 2
                }
            },
            'studies': [
                {'id': 'test1', 'title': 'Test Study 1', 'year': 2024},
                {'id': 'test2', 'title': 'Test Study 2', 'year': 2023}
            ]
        }
        
        logger.info("✅ 测试数据准备完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ DataVisualizer 创建失败: {e}")
        return False

def test_content_filter_handling():
    """测试内容过滤处理"""
    try:
        from llm_manager import ContentFilteredException
        
        # 测试异常创建
        exception = ContentFilteredException("Test filter")
        logger.info("✅ ContentFilteredException 创建成功")
        
        # 测试在evidence_processor中的导入
        from evidence_processor import EvidenceProcessor
        logger.info("✅ EvidenceProcessor 导入成功（包含ContentFilteredException处理）")
        
        return True
    except Exception as e:
        logger.error(f"❌ 内容过滤处理测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    logger.info("🚀 开始测试可视化修复...")
    
    tests = [
        ("可视化模块导入", test_visualization_import),
        ("EBM生成器导入", test_ebm_generator_import),
        ("DataVisualizer创建", test_data_visualizer_creation),
        ("内容过滤处理", test_content_filter_handling)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    logger.info("\n📊 测试结果总结:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        logger.info("🎉 所有测试通过！可视化修复成功。")
        return True
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    print("\n" + "="*50)
    if success:
        print("✅ 可视化修复验证成功！")
        print("📊 现在应该能够正确生成可视化图表")
        print("🔧 偏倚风险评估数据应该正确显示")
        print("📝 报告内容应该完整，不再截断")
    else:
        print("❌ 修复验证失败，需要进一步调试")
    print("="*50)
    sys.exit(0 if success else 1)
