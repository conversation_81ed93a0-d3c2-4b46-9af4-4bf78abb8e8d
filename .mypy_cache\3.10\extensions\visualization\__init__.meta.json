{"data_mtime": 1751193016, "dep_lines": [6, 7, 8, 9, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["extensions.visualization.forest_plot", "extensions.visualization.funnel_plot", "extensions.visualization.rob_plot", "extensions.visualization.data_visualizer", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "28888eda1c0aad349b8cc8291fdb191331c1c43f", "id": "extensions.visualization", "ignore_all": false, "interface_hash": "35bbb7a8963fda747494c653af83fd09b8efad57", "mtime": 1751193011, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\extensions\\visualization\\__init__.py", "plugin_data": null, "size": 310, "suppressed": [], "version_id": "1.15.0"}