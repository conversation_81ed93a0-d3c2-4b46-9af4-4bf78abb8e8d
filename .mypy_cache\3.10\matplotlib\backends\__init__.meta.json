{"data_mtime": 1751189338, "dep_lines": [1, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30], "dependencies": ["matplotlib.backends.registry", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "58a404efdd9bdb6db47b67dfb3bbbe21ef59696d", "id": "matplotlib.backends", "ignore_all": true, "interface_hash": "423744a3d0f49c5168e8b13f1268a8e2a27c5a1b", "mtime": 1751088276, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\xzyxgg\\venv\\lib\\site-packages\\matplotlib\\backends\\__init__.py", "plugin_data": null, "size": 206, "suppressed": [], "version_id": "1.15.0"}