{"data_mtime": 1751183034, "dep_lines": [6, 7, 8, 9, 128, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["sys", "json", "logging", "typing", "evidence_processor", "builtins", "_frozen_importlib", "_typeshed", "abc", "json.encoder", "os", "typing_extensions"], "hash": "a3b84bb4ba2aaa7e01997e74c8eb17fd0cded285", "id": "test_data_extraction", "ignore_all": false, "interface_hash": "ba53971b0b7dae525ac349204eb4c9a6f395f293", "mtime": 1751183031, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\test_data_extraction.py", "plugin_data": null, "size": 7558, "suppressed": [], "version_id": "1.15.0"}