{"data_mtime": 1751189803, "dep_lines": [35, 10, 11, 12, 13, 22, 89, 116, 134, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 10, 10, 10, 5, 20, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["extensions.visualization.data_visualizer", "logging", "os", "sys", "pathlib", "llm_manager", "ebm_professional_enhancements", "evidence_processor", "ebm_generator", "builtins", "_frozen_importlib", "_typeshed", "abc", "extensions", "extensions.visualization", "typing"], "hash": "4f16985a46afc919547d944cb81e055643588c98", "id": "test_fixes", "ignore_all": false, "interface_hash": "cbc0724a4eed14ff1214bdd6c0073cf9d23eb045", "mtime": 1751189799, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\test_fixes.py", "plugin_data": null, "size": 7040, "suppressed": [], "version_id": "1.15.0"}