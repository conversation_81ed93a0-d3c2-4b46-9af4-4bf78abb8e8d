{"data_mtime": 1751194880, "dep_lines": [17, 21, 6, 7, 25, 96, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 20, 10, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["extensions.visualization.data_visualizer", "extensions.visualization", "logging", "sys", "ebm_generator", "llm_manager", "builtins", "_frozen_importlib", "_typeshed", "abc", "extensions", "os", "typing"], "hash": "e68fd9d732182ed1b5a92e262429ee76ed7e650c", "id": "test_visualization_debug", "ignore_all": false, "interface_hash": "13fc390542aa82c975f92c47d35b68e9b1ed9129", "mtime": 1751194875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\test_visualization_debug.py", "plugin_data": null, "size": 7019, "suppressed": [], "version_id": "1.15.0"}