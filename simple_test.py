#!/usr/bin/env python3
"""
简化测试脚本
"""

import sys
import os

def test_imports():
    """测试关键模块导入"""
    try:
        # 测试内容过滤异常
        from llm_manager import ContentFilteredException
        print("✅ ContentFilteredException 导入成功")
        
        # 测试证据处理器
        from evidence_processor import EvidenceProcessor
        print("✅ EvidenceProcessor 导入成功")
        
        # 测试专业处理器
        from ebm_professional_enhancements import ProfessionalEBMProcessor
        print("✅ ProfessionalEBMProcessor 导入成功")
        
        # 测试EBM生成器
        from ebm_generator import EBMGenerator
        print("✅ EBMGenerator 导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def test_visualization_import():
    """测试可视化模块导入"""
    try:
        # 检查目录结构
        if not os.path.exists("extensions/visualization"):
            print("❌ extensions/visualization 目录不存在")
            return False
            
        if not os.path.exists("extensions/visualization/data_visualizer.py"):
            print("❌ data_visualizer.py 文件不存在")
            return False
            
        print("✅ 可视化文件结构正确")
        return True
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        return False

def main():
    print("🚀 开始简化测试...")
    
    tests = [
        ("模块导入", test_imports),
        ("可视化结构", test_visualization_import)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        if test_func():
            passed += 1
    
    print(f"\n🎯 结果: {passed}/{len(tests)} 个测试通过")
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    print("🎉 测试完成" if success else "⚠️ 测试失败")
