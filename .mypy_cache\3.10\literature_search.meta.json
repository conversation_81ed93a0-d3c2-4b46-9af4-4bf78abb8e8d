{"data_mtime": 1751187728, "dep_lines": [6, 9, 10, 254, 255, 2, 3, 4, 5, 6, 9, 10, 14, 15, 16, 222, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 11], "dep_prios": [5, 10, 10, 20, 20, 10, 10, 5, 5, 20, 20, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10], "dependencies": ["concurrent.futures", "Bio.Entrez", "tenacity.retry", "urllib.error", "http.client", "logging", "re", "datetime", "typing", "concurrent", "Bio", "tenacity", "config", "llm_manager", "prompts", "time", "builtins", "_frozen_importlib", "_typeshed", "abc", "concurrent.futures._base", "os", "tenacity.asyncio", "tenacity.asyncio.retry", "tenacity.stop", "tenacity.wait", "types", "typing_extensions"], "hash": "f224df4ad4a372a1452c3a0b23f40b42e2c9cb12", "id": "literature_search", "ignore_all": true, "interface_hash": "66d7a901fdce038a1e4540de6cb88a4a2b29ffa6", "mtime": 1751187692, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\literature_search.py", "plugin_data": null, "size": 40248, "suppressed": ["arxiv", "requests"], "version_id": "1.15.0"}