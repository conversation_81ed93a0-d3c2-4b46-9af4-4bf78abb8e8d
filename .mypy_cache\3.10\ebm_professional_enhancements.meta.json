{"data_mtime": 1751189307, "dep_lines": [7, 8, 9, 10, 11, 12, 13, 14, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["json", "logging", "re", "typing", "collections", "dataclasses", "enum", "llm_manager", "builtins", "_frozen_importlib", "abc"], "hash": "501e669819faca7e42caebf5f587d8ea9c0fe740", "id": "ebm_professional_enhancements", "ignore_all": true, "interface_hash": "bf92bd1802945581c20a29ba86f88c407159caa2", "mtime": 1751189262, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\ebm_professional_enhancements.py", "plugin_data": null, "size": 23074, "suppressed": [], "version_id": "1.15.0"}