#!/usr/bin/env python3
"""
测试修复的脚本
验证：
1. 内容过滤异常处理
2. 偏倚风险评估数据
3. 数据可视化生成
"""

import logging
import os
import sys
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_content_filter_exception():
    """测试内容过滤异常处理"""
    try:
        from llm_manager import ContentFilteredException
        
        # 测试异常创建
        exception = ContentFilteredException("Test content filter")
        logger.info(f"✅ ContentFilteredException 创建成功: {exception}")
        return True
    except Exception as e:
        logger.error(f"❌ ContentFilteredException 测试失败: {e}")
        return False

def test_data_visualizer():
    """测试数据可视化器"""
    try:
        from extensions.visualization.data_visualizer import DataVisualizer
        
        # 创建测试数据
        test_data = {
            'search_stats': {
                'identified': 100,
                'after_duplicates': 80,
                'screened': 70,
                'full_text_assessed': 50,
                'included_qualitative': 30,
                'included_quantitative': 25,
                'duplicates_removed': 20,
                'excluded_screening': 20,
                'excluded_full_text': 20
            },
            'quality_summary': {
                'quality_distribution': {
                    'High': 5,
                    'Moderate': 10,
                    'Low': 8,
                    'Very Low': 2
                },
                'bias_risk_summary': {
                    'random_sequence_generation': {'Low': 5, 'Unclear': 3, 'High': 2},
                    'allocation_concealment': {'Low': 4, 'Unclear': 4, 'High': 2}
                }
            },
            'studies': [
                {'id': 'test1', 'title': 'Test Study 1', 'year': 2024},
                {'id': 'test2', 'title': 'Test Study 2', 'year': 2023}
            ]
        }
        
        # 创建可视化器
        visualizer = DataVisualizer("test_output")
        
        # 生成图表
        charts = visualizer.generate_all_visualizations(test_data)
        
        logger.info(f"✅ 数据可视化器测试成功，生成了 {len(charts)} 个图表")
        for chart_name, chart_path in charts.items():
            if os.path.exists(chart_path):
                logger.info(f"  - {chart_name}: {chart_path}")
            else:
                logger.warning(f"  - {chart_name}: 文件不存在 {chart_path}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 数据可视化器测试失败: {e}")
        return False

def test_quality_assessment():
    """测试质量评估功能"""
    try:
        from ebm_professional_enhancements import ProfessionalEBMProcessor
        from llm_manager import LLMManager
        
        # 创建测试实例
        llm_manager = LLMManager()
        processor = ProfessionalEBMProcessor(llm_manager)
        
        # 创建测试研究
        test_study = {
            'id': 'test_study',
            'title': 'Test Study for Quality Assessment',
            'abstract': 'This is a test study abstract for quality assessment.',
            'year': 2024,
            'authors': ['Test Author']
        }
        
        logger.info("✅ 质量评估模块初始化成功")
        logger.info("注意：实际质量评估需要LLM调用，此处仅测试模块加载")
        
        return True
    except Exception as e:
        logger.error(f"❌ 质量评估测试失败: {e}")
        return False

def test_evidence_processor_with_filter():
    """测试证据处理器的内容过滤处理"""
    try:
        from evidence_processor import EvidenceProcessor
        from llm_manager import LLMManager, ContentFilteredException
        
        # 创建测试实例
        llm_manager = LLMManager()
        processor = EvidenceProcessor(llm_manager)
        
        logger.info("✅ 证据处理器初始化成功")
        logger.info("✅ ContentFilteredException 导入成功")
        
        return True
    except Exception as e:
        logger.error(f"❌ 证据处理器测试失败: {e}")
        return False

def test_ebm_generator_visualization():
    """测试EBM生成器的可视化功能"""
    try:
        from ebm_generator import EBMGenerator
        from llm_manager import LLMManager
        
        # 创建测试实例
        llm_manager = LLMManager()
        
        def dummy_callback(message, is_error=False):
            logger.info(f"回调: {message}")
        
        generator = EBMGenerator(llm_manager, dummy_callback)
        
        # 测试可视化数据准备
        test_studies = [
            {'id': 'test1', 'title': 'Test Study 1', 'year': 2024},
            {'id': 'test2', 'title': 'Test Study 2', 'year': 2023}
        ]
        
        viz_data = generator._prepare_comprehensive_visualization_data(test_studies)
        
        if viz_data and 'search_stats' in viz_data:
            logger.info("✅ EBM生成器可视化数据准备成功")
            logger.info(f"  - 搜索统计: {viz_data['search_stats']}")
            return True
        else:
            logger.error("❌ 可视化数据准备失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ EBM生成器可视化测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    logger.info("🚀 开始运行修复验证测试...")
    
    tests = [
        ("内容过滤异常处理", test_content_filter_exception),
        ("数据可视化器", test_data_visualizer),
        ("质量评估功能", test_quality_assessment),
        ("证据处理器内容过滤", test_evidence_processor_with_filter),
        ("EBM生成器可视化", test_ebm_generator_visualization)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    logger.info("\n📊 测试结果总结:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        logger.info("🎉 所有测试通过！修复成功。")
        return True
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
