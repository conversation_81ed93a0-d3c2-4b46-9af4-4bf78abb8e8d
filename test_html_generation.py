#!/usr/bin/env python3
"""
测试HTML报告生成功能
"""

import os
import sys
import logging
from typing import Dict, List, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 模拟的研究数据
SAMPLE_STUDIES = [
    {
        "id": "12345678",
        "source": "PubMed",
        "title": "Effect of metformin on cardiovascular outcomes in diabetes: a randomized controlled trial",
        "abstract": "Background: Metformin is widely used for diabetes treatment. Methods: We conducted a randomized controlled trial with 1000 patients. Results: Metformin reduced cardiovascular events with OR 0.75 (95% CI: 0.60-0.94, p=0.012). The relative risk was 0.78 (95% CI: 0.65-0.93). Conclusion: Metformin shows cardiovascular benefits.",
        "authors": ["Smith J", "Johnson A", "Brown K"],
        "journal": "Diabetes Care",
        "year": "2023",
        "url": "https://pubmed.ncbi.nlm.nih.gov/12345678/",
        "doi": "10.2337/dc23-0001",
        "is_clinical": True,
        "ebm_data": {
            "is_clinical": True,
            "pico": {
                "population": "patients with diabetes",
                "intervention": "metformin",
                "comparison": "placebo",
                "outcome": "cardiovascular events"
            },
            "results": {
                "effect_size": "OR",
                "effect_value": 0.75,
                "ci_lower": 0.60,
                "ci_upper": 0.94,
                "p_value": "p=0.012"
            },
            "study_design": "RCT",
            "sample_size": 1000,
            "quality_indicators": {
                "randomized": True,
                "blinded": True,
                "controlled": True
            }
        },
        "pico": {
            "population": "patients with diabetes",
            "intervention": "metformin",
            "comparison": "placebo",
            "outcome": "cardiovascular events",
            "finding": "Metformin reduced cardiovascular events with OR 0.75 (95% CI: 0.60-0.94, p=0.012)"
        }
    },
    {
        "id": "87654321",
        "source": "PubMed", 
        "title": "Statin therapy and stroke prevention: a systematic review and meta-analysis",
        "abstract": "We analyzed 15 randomized trials including 45,000 patients. Statin therapy significantly reduced stroke risk (RR 0.82, 95% CI 0.74-0.91, p<0.001). The mean difference in LDL cholesterol was -1.2 mmol/L (95% CI: -1.5 to -0.9).",
        "authors": ["Wilson M", "Davis R"],
        "journal": "Stroke",
        "year": "2023",
        "url": "https://pubmed.ncbi.nlm.nih.gov/87654321/",
        "doi": "10.1161/str.2023.001",
        "is_clinical": True,
        "ebm_data": {
            "is_clinical": True,
            "pico": {
                "population": "patients at risk of stroke",
                "intervention": "statin therapy",
                "comparison": "placebo or no treatment",
                "outcome": "stroke incidence"
            },
            "results": {
                "effect_size": "RR",
                "effect_value": 0.82,
                "ci_lower": 0.74,
                "ci_upper": 0.91,
                "p_value": "p<0.001"
            },
            "study_design": "Meta-analysis",
            "sample_size": 45000,
            "quality_indicators": {
                "randomized": True,
                "blinded": False,
                "controlled": True
            }
        },
        "pico": {
            "population": "patients at risk of stroke",
            "intervention": "statin therapy",
            "comparison": "placebo or no treatment",
            "outcome": "stroke incidence",
            "finding": "Statin therapy significantly reduced stroke risk (RR 0.82, 95% CI 0.74-0.91, p<0.001)"
        }
    }
]

def test_html_generation():
    """测试HTML报告生成功能"""
    try:
        # 导入必要的模块
        from llm_manager import LLMManager
        from ebm_generator import EBMGenerator
        
        # 创建LLM管理器
        llm_manager = LLMManager()
        
        # 创建EBM生成器
        ebm_generator = EBMGenerator(llm_manager)
        
        logger.info("开始测试HTML报告生成...")
        
        # 测试生成报告
        topic = "糖尿病药物治疗的心血管保护作用"
        
        try:
            # 生成报告
            report_cn_path, report_en_path = ebm_generator.generate_reports(
                topic=topic,
                articles_for_report=SAMPLE_STUDIES,
                report_type="ebm",
                provider="zhipuai",
                model="glm-4-flash"
            )
            
            logger.info(f"中文报告路径: {report_cn_path}")
            logger.info(f"英文报告路径: {report_en_path}")
            
            # 检查Markdown文件是否存在
            if report_cn_path and os.path.exists(report_cn_path):
                logger.info(f"✅ 中文Markdown报告生成成功: {report_cn_path}")
                
                # 检查HTML文件是否存在
                html_path_cn = report_cn_path.replace('.md', '.html')
                if os.path.exists(html_path_cn):
                    logger.info(f"✅ 中文HTML报告生成成功: {html_path_cn}")
                    
                    # 检查HTML文件大小
                    html_size = os.path.getsize(html_path_cn)
                    logger.info(f"   HTML文件大小: {html_size} bytes")
                    
                    # 读取HTML内容的前500个字符
                    with open(html_path_cn, 'r', encoding='utf-8') as f:
                        html_content = f.read()
                        logger.info(f"   HTML内容预览: {html_content[:500]}...")
                        
                        # 检查是否包含可视化内容
                        if 'mermaid' in html_content.lower():
                            logger.info("   ✅ HTML包含Mermaid图表")
                        else:
                            logger.warning("   ⚠️ HTML不包含Mermaid图表")
                            
                        if 'forest' in html_content.lower():
                            logger.info("   ✅ HTML包含森林图")
                        else:
                            logger.warning("   ⚠️ HTML不包含森林图")
                            
                else:
                    logger.error(f"❌ 中文HTML报告未生成: {html_path_cn}")
            else:
                logger.error(f"❌ 中文Markdown报告未生成: {report_cn_path}")
            
            # 检查英文报告
            if report_en_path and os.path.exists(report_en_path):
                logger.info(f"✅ 英文Markdown报告生成成功: {report_en_path}")
                
                html_path_en = report_en_path.replace('.md', '.html')
                if os.path.exists(html_path_en):
                    logger.info(f"✅ 英文HTML报告生成成功: {html_path_en}")
                    html_size = os.path.getsize(html_path_en)
                    logger.info(f"   HTML文件大小: {html_size} bytes")
                else:
                    logger.error(f"❌ 英文HTML报告未生成: {html_path_en}")
            else:
                logger.error(f"❌ 英文Markdown报告未生成: {report_en_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"报告生成过程中出错: {str(e)}", exc_info=True)
            return False
        
    except Exception as e:
        logger.error(f"测试初始化失败: {str(e)}", exc_info=True)
        return False

def test_visualization_availability():
    """测试可视化模块可用性"""
    try:
        # 检查可视化模块
        from ebm_generator import VISUALIZATION_AVAILABLE
        logger.info(f"可视化模块可用性: {VISUALIZATION_AVAILABLE}")
        
        if VISUALIZATION_AVAILABLE:
            try:
                from extensions.visualization.forest_plot_generator import ForestPlotGenerator
                logger.info("✅ 森林图生成器导入成功")
            except Exception as e:
                logger.error(f"❌ 森林图生成器导入失败: {str(e)}")
            
            try:
                from extensions.visualization.html_report_generator import HTMLReportGenerator
                logger.info("✅ HTML报告生成器导入成功")
            except Exception as e:
                logger.error(f"❌ HTML报告生成器导入失败: {str(e)}")
        else:
            logger.warning("⚠️ 可视化模块不可用")
            
    except Exception as e:
        logger.error(f"检查可视化模块时出错: {str(e)}")

if __name__ == "__main__":
    logger.info("=== HTML报告生成测试 ===")
    
    # 测试可视化模块可用性
    test_visualization_availability()
    
    # 测试HTML生成
    success = test_html_generation()
    
    if success:
        logger.info("✅ 测试完成")
        sys.exit(0)
    else:
        logger.error("❌ 测试失败")
        sys.exit(1)
