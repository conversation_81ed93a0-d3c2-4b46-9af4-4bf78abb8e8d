{"data_mtime": 1751189198, "dep_lines": [4, 2, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["tenacity.retry", "json", "logging", "tenacity", "config", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "datetime", "os", "tenacity.asyncio", "tenacity.asyncio.retry", "tenacity.stop", "tenacity.wait"], "hash": "9076a956b5337229a78b90e05dbfef6ce9547a74", "id": "llm_manager", "ignore_all": true, "interface_hash": "b147f5c745634419c01aa17309c2bf95dade3251", "mtime": 1751189152, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\llm_manager.py", "plugin_data": null, "size": 10651, "suppressed": ["requests"], "version_id": "1.15.0"}