{"data_mtime": 1751189338, "dep_lines": [1, 5, 2, 4, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "matplotlib.typing", "contextlib", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins", "_frozen_importlib", "abc", "os", "pathlib", "typing"], "hash": "0f27cd4e002e04db58babd5dc7b13668ddb02d6a", "id": "matplotlib.style.core", "ignore_all": true, "interface_hash": "cffbdfe3bbb789026b18f5c2ffc031e46c4b28ac", "mtime": 1751088276, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\xzyxgg\\venv\\lib\\site-packages\\matplotlib\\style\\core.pyi", "plugin_data": null, "size": 521, "suppressed": [], "version_id": "1.15.0"}