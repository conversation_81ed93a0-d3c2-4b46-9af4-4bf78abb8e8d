[{"id": "39316770", "source": "PubMed", "title": "CDC Program Evaluation Framework, 2024.", "abstract": "Program evaluation is a critical tool for understanding and improving organizational activities and systems. This report updates the 1999 CDC Framework for Program Evaluation in Public Health (CDC. Framework for program evaluation in public health. MMWR Recomm Rep 1999;48[No. RR-11];1-40) by integrating major advancements in the fields of evaluation and public health, lessons learned from practical applications of the original framework, and current Federal agency policies and practices. A practical, nonprescriptive tool, the updated 2024 framework is designed to summarize and organize essential elements of program evaluation, and can be applied at any level from individual programs to broader systems by novices and experts for planning and implementing an evaluation. Although many of the key aspects from the 1999 framework remain, certain key differences exist. For example, this updated framework also includes six steps that describe the general process of evaluation planning and implementation, but some content and step names have changed (e.g., the first step has been renamed Assess context). The standards for high-quality evaluation remain central to the framework, although they have been updated to the five Federal evaluation standards. The most substantial change from the 1999 framework is the addition of three cross-cutting actions that are core tenets to incorporate within each evaluation step: engage collaboratively, advance equity, and learn from and use insights. The 2024 framework provides a guide for designing and conducting evaluation across many topics within and outside of public health that anyone involved in program evaluation efforts can use alone or in conjunction with other evaluation approaches, tools, or methods to build evidence, understand programs, and refine evidence-based decision-making to improve all program outcomes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Euna August", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "journal": "MMWR. Recommendations and reports : Morbidity and mortality weekly report. Recommendations and reports", "year": "2024", "url": "https://pubmed.ncbi.nlm.nih.gov/39316770/", "doi": "", "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"comparison": "s for high-quality evaluation remain central to the framework; s"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2312.05982v1", "source": "ArXiv", "title": "Building up a model family for inflammations", "abstract": "The paper presents an approach for overcoming modeling problems of typical life science applications with partly unknown mechanisms and lacking quantitative data: A model family of reaction diffusion equations is built up on a mesoscopic scale and uses classes of feasible functions for reaction and taxis terms. The classes are found by translating biological knowledge into mathematical conditions and the analysis of the models further constrains the classes. Numerical simulations allow comparing single models out of the model family with available qualitative information on the solutions from observations. The method provides insight into a hierarchical order of the mechanisms. The method is applied to the clinics for liver inflammation such as metabolic dysfunction-associated steatohepatitis (MASH) or viral hepatitis where reasons for the chronification of disease are still unclear and time- and space-dependent data is unavailable.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2023", "url": "http://arxiv.org/pdf/2312.05982v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2311.07595v2", "source": "ArXiv", "title": "A Diagnosis and Treatment of Liver Diseases: Integrating Batch Processing, Rule-Based Event Detection and Explainable Artificial Intelligence", "abstract": "Liver diseases pose a significant global health burden, impacting many individuals and having substantial economic and social consequences. Rising liver problems are considered a fatal disease in many countries, such as Egypt and Moldova. This study aims to develop a diagnosis and treatment model for liver disease using Basic Formal Ontology (BFO), Patient Clinical Data (PCD) ontology, and detection rules derived from a decision tree algorithm. For the development of the ontology, the National Viral Hepatitis Control Program (NVHCP) guidelines were used, which made the ontology more accurate and reliable. The Apache Jena framework uses batch processing to detect events based on these rules. Based on the event detected, queries can be directly processed using SPARQL. We convert these Decision Tree (DT) and medical guidelines-based rules into Semantic Web Rule Language (SWRL) to operationalize the ontology. Using this SWRL in the ontology to predict different types of liver disease with the help of the Pellet and Drools inference engines in Protege Tools, a total of 615 records were taken from different liver diseases. After inferring the rules, the result can be generated for the patient according to the rules, and other patient-related details, along with different precautionary suggestions, can be obtained based on these results. These rules can make suggestions more accurate with the help of Explainable Artificial Intelligence (XAI) with open API-based suggestions. When the patient has prescribed a medical test, the model accommodates this result using optical character recognition (OCR), and the same process applies when the patient has prescribed a further medical suggestion according to the test report. These models combine to form a comprehensive Decision Support System (DSS) for the diagnosis of liver disease.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "journal": "ArXiv", "year": "2023", "url": "http://arxiv.org/pdf/2311.07595v2", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"intervention": "model for liver disease using Basic Formal Ontology", "comparison": "Program"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": true}}}, {"id": "2011.03359v2", "source": "ArXiv", "title": "A New Inference algorithm of Dynamic Uncertain Causality Graph based on Conditional Sampling Method for Complex Cases", "abstract": "Dynamic Uncertain Causality Graph(DUCG) is a recently proposed model for diagnoses of complex systems. It performs well for industry system such as nuclear power plants, chemical system and spacecrafts. However, the variable state combination explosion in some cases is still a problem that may result in inefficiency or even disability in DUCG inference. In the situation of clinical diagnoses, when a lot of intermediate causes are unknown while the downstream results are known in a DUCG graph, the combination explosion may appear during the inference computation. Monte Carlo sampling is a typical algorithm to solve this problem. However, we are facing the case that the occurrence rate of the case is very small, e.g. $10^{-20}$, which means a huge number of samplings are needed. This paper proposes a new scheme based on conditional stochastic simulation which obtains the final result from the expectation of the conditional probability in sampling loops instead of counting the sampling frequency, and thus overcomes the problem. As a result, the proposed algorithm requires much less time than the DUCG recursive inference algorithm presented earlier. Moreover, a simple analysis of convergence rate based on a designed example is given to show the advantage of the proposed method. % In addition, supports for logic gate, logic cycles, and parallelization, which exist in DUCG, are also addressed in this paper. The new algorithm reduces the time consumption a lot and performs 3 times faster than old one with 2.7% error ratio in a practical graph for Viral Hepatitis B.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2020", "url": "http://arxiv.org/pdf/2011.03359v2", "doi": "10.1109/ACCESS.2021.3093205", "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}]